import React, { useState } from 'react';
import { HelpCircle, Book, Search, ChevronRight, ArrowLeft, Lightbulb, Settings, Calendar, Bell, Camera, Brain } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { motion, AnimatePresence } from 'framer-motion';

interface HelpSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  description: string;
  articles: HelpArticle[];
}

interface HelpArticle {
  id: string;
  title: string;
  content: string;
  tags: string[];
}

/**
 * Composant principal du centre d'aide
 */
export const HelpCenter: React.FC = () => {
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [activeArticle, setActiveArticle] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const helpSections: HelpSection[] = [
    {
      id: 'getting-started',
      title: 'Premiers pas',
      icon: <Lightbulb className="h-5 w-5" />,
      description: 'Découvrez comment utiliser FloraSynth pour prendre soin de vos plantes',
      articles: [
        {
          id: 'welcome',
          title: 'Bienvenue dans FloraSynth',
          content: `
# Bienvenue dans FloraSynth 🌱

FloraSynth est votre assistant intelligent pour prendre soin de vos plantes. Cette application utilise l'intelligence artificielle Google Gemini pour vous aider à diagnostiquer les problèmes de vos plantes et vous donner des conseils personnalisés.

## Fonctionnalités principales :

- **Diagnostic IA** : Prenez une photo de votre plante et obtenez un diagnostic instantané
- **Calendrier de soins** : Planifiez et suivez les soins de vos plantes
- **Notifications intelligentes** : Recevez des rappels personnalisés
- **Journal global** : Consultez l'historique de toutes vos actions
- **Recommandations** : L'IA vous propose des actions préventives

## Comment commencer :

1. Ajoutez votre première plante depuis le tableau de bord
2. Prenez une photo pour un premier diagnostic
3. Configurez vos notifications dans les paramètres
4. Explorez le calendrier pour planifier vos soins
          `,
          tags: ['débutant', 'introduction', 'fonctionnalités']
        },
        {
          id: 'add-first-plant',
          title: 'Ajouter votre première plante',
          content: `
# Ajouter votre première plante 🪴

## Étapes pour ajouter une plante :

1. **Accédez au tableau de bord** : Cliquez sur "Mes Plantes" dans le menu principal
2. **Cliquez sur "Ajouter une plante"** : Le bouton violet avec l'icône "+"
3. **Remplissez les informations** :
   - **Nom** : Donnez un nom à votre plante (ex: "Mon Monstera")
   - **Espèce** : Indiquez l'espèce si vous la connaissez (ex: "Monstera deliciosa")
   - **Description** : Décrivez l'état actuel de votre plante (couleur des feuilles, problèmes observés, etc.)

## Conseils pour la description :

- Mentionnez la couleur des feuilles
- Notez si vous voyez des taches, jaunissement, ou flétrissement
- Indiquez l'emplacement de la plante (intérieur/extérieur, exposition au soleil)
- Précisez la fréquence d'arrosage actuelle

Cette description aidera l'IA à mieux comprendre votre plante lors des futurs diagnostics !
          `,
          tags: ['plante', 'ajout', 'débutant']
        }
      ]
    },
    {
      id: 'diagnosis',
      title: 'Diagnostic IA',
      icon: <Camera className="h-5 w-5" />,
      description: 'Apprenez à utiliser le diagnostic par intelligence artificielle',
      articles: [
        {
          id: 'how-to-diagnose',
          title: 'Comment faire un diagnostic',
          content: `
# Comment faire un diagnostic IA 📸

Le diagnostic IA est l'une des fonctionnalités les plus puissantes de Violet Rikita. Voici comment l'utiliser efficacement :

## Prendre une bonne photo :

1. **Éclairage** : Utilisez la lumière naturelle si possible
2. **Angle** : Prenez la photo à hauteur de la plante
3. **Distance** : Ni trop près, ni trop loin - la plante doit occuper la majeure partie de l'image
4. **Focus** : Assurez-vous que l'image est nette, surtout sur les zones problématiques

## Types de photos recommandées :

- **Vue d'ensemble** : Pour évaluer l'état général
- **Feuilles en détail** : Pour identifier les maladies ou carences
- **Racines** : Si vous suspectez un problème racinaire
- **Tiges** : Pour détecter les parasites ou maladies

## Après la photo :

1. L'IA analyse automatiquement l'image
2. Vous recevez un diagnostic détaillé
3. Des recommandations de traitement sont proposées
4. Un événement peut être créé automatiquement dans votre calendrier

## Conseils pour de meilleurs résultats :

- Nettoyez l'objectif de votre appareil photo
- Évitez les reflets et les ombres fortes
- Prenez plusieurs photos sous différents angles si nécessaire
          `,
          tags: ['diagnostic', 'photo', 'IA', 'conseils']
        }
      ]
    },
    {
      id: 'calendar',
      title: 'Calendrier et événements',
      icon: <Calendar className="h-5 w-5" />,
      description: 'Gérez vos soins et planifiez les actions pour vos plantes',
      articles: [
        {
          id: 'calendar-overview',
          title: 'Utiliser le calendrier',
          content: `
# Utiliser le calendrier 📅

Le calendrier vous aide à organiser tous les soins de vos plantes en un seul endroit.

## Créer un nouvel événement :

1. Cliquez sur "Nouvel événement" dans le calendrier
2. Sélectionnez la plante concernée
3. Choisissez le type d'action (arrosage, fertilisation, etc.)
4. Définissez la date et l'heure
5. Ajoutez une description détaillée

## Types d'événements :

- **Arrosage** : Planifiez les sessions d'arrosage
- **Fertilisation** : Programmez l'apport d'engrais
- **Rempotage** : Planifiez le changement de pot
- **Taille** : Organisez les sessions de taille
- **Traitement** : Programmez l'application de traitements

## Vues disponibles :

- **Vue mensuelle** : Aperçu global du mois
- **Vue hebdomadaire** : Détail de la semaine
- **Vue liste** : Tous les événements en liste

## Notifications automatiques :

Le système vous enverra des rappels avant chaque événement selon vos préférences.
          `,
          tags: ['calendrier', 'événements', 'planification']
        }
      ]
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: <Bell className="h-5 w-5" />,
      description: 'Configurez et gérez vos notifications et rappels',
      articles: [
        {
          id: 'notification-settings',
          title: 'Configurer les notifications',
          content: `
# Configurer les notifications 🔔

Les notifications vous aident à ne jamais oublier de prendre soin de vos plantes.

## Types de notifications :

1. **Rappels d'arrosage** : Basés sur les besoins de chaque plante
2. **Alertes de diagnostic** : Quand l'IA détecte un problème
3. **Recommandations préventives** : Conseils pour éviter les problèmes
4. **Événements du calendrier** : Rappels de vos tâches planifiées

## Paramètres disponibles :

- **Fréquence** : Choisissez la fréquence des vérifications IA
- **Priorité minimale** : Filtrez les notifications par importance
- **Alertes de sécurité** : Notifications urgentes pour les problèmes graves
- **Recommandations automatiques** : Laissez l'IA vous proposer des actions

## Gestion des notifications :

- Marquez les notifications comme lues
- Filtrez par type ou priorité
- Exportez l'historique pour analyse
- Configurez les préférences par plante

## Conseils :

- Commencez avec des notifications fréquentes puis ajustez
- Activez toujours les alertes de sécurité
- Consultez régulièrement le centre de notifications
          `,
          tags: ['notifications', 'rappels', 'configuration']
        }
      ]
    },
    {
      id: 'ai-settings',
      title: 'Paramètres IA',
      icon: <Brain className="h-5 w-5" />,
      description: 'Configurez l\'intelligence artificielle Gemini',
      articles: [
        {
          id: 'gemini-config',
          title: 'Configurer Gemini IA',
          content: `
# Configurer Gemini IA 🤖

L'intelligence artificielle Gemini est le cœur de Violet Rikita. Voici comment l'optimiser :

## Paramètres principaux :

1. **Recommandations automatiques** : L'IA propose des actions préventives
2. **Fréquence d'analyse** : Définissez à quelle fréquence l'IA vérifie vos plantes
3. **Alertes de sécurité** : Notifications urgentes pour les problèmes graves
4. **Priorité minimale** : Filtrez les recommandations par importance

## Fréquences recommandées :

- **6 heures** : Pour les plantes fragiles ou malades
- **24 heures** : Recommandé pour la plupart des plantes
- **48-72 heures** : Pour les plantes robustes et en bonne santé

## Niveaux de priorité :

- **Faible** : Conseils généraux d'entretien
- **Moyenne** : Recommandations importantes
- **Élevée** : Actions nécessaires rapidement
- **Urgente** : Intervention immédiate requise

## Optimisation :

- L'IA apprend de vos actions et s'améliore avec le temps
- Plus vous utilisez l'application, plus les recommandations sont précises
- N'hésitez pas à prendre plusieurs photos pour de meilleurs diagnostics

## Modèle utilisé :

Violet Rikita utilise **Gemini 2.5 Flash**, le modèle le plus récent de Google, optimisé pour l'analyse d'images et les recommandations contextuelles.
          `,
          tags: ['IA', 'Gemini', 'configuration', 'optimisation']
        }
      ]
    }
  ];

  // Filtrage des articles par terme de recherche
  const filteredSections = helpSections.map(section => ({
    ...section,
    articles: section.articles.filter(article =>
      searchTerm === '' ||
      article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  })).filter(section => section.articles.length > 0);

  const currentSection = helpSections.find(s => s.id === activeSection);
  const currentArticle = currentSection?.articles.find(a => a.id === activeArticle);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <HelpCircle className="h-8 w-8 text-[#d385f5]" />
          <div>
            <h1 className="text-2xl font-bold text-white">Centre d'aide</h1>
            <p className="text-[#E0E0E0]">Tout ce que vous devez savoir sur Violet Rikita</p>
          </div>
        </div>
        
        {(activeSection || activeArticle) && (
          <Button
            variant="ghost"
            onClick={() => {
              if (activeArticle) {
                setActiveArticle(null);
              } else {
                setActiveSection(null);
              }
            }}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Retour
          </Button>
        )}
      </div>

      {/* Barre de recherche */}
      {!activeArticle && (
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Rechercher dans l'aide..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-[#1c1a31] border border-gray-600 rounded-lg text-white focus:border-[#d385f5] focus:outline-none"
          />
        </div>
      )}

      <AnimatePresence mode="wait">
        {activeArticle && currentArticle ? (
          // Vue article
          <motion.div
            key="article"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Book className="h-5 w-5 text-[#d385f5]" />
                  {currentArticle.title}
                </CardTitle>
                <div className="flex gap-2">
                  {currentArticle.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardHeader>
              <CardContent>
                <div className="prose prose-invert max-w-none">
                  <div 
                    className="text-[#E0E0E0] leading-relaxed whitespace-pre-line"
                    dangerouslySetInnerHTML={{ 
                      __html: currentArticle.content
                        .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold text-white mb-4">$1</h1>')
                        .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold text-white mb-3 mt-6">$1</h2>')
                        .replace(/^\*\*(.*?)\*\*/gm, '<strong class="text-white">$1</strong>')
                        .replace(/^\- (.*$)/gm, '<li class="ml-4">$1</li>')
                        .replace(/^(\d+)\. (.*$)/gm, '<li class="ml-4 list-decimal">$2</li>')
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ) : activeSection && currentSection ? (
          // Vue section
          <motion.div
            key="section"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <div className="flex items-center gap-3 mb-6">
              {currentSection.icon}
              <div>
                <h2 className="text-xl font-bold text-white">{currentSection.title}</h2>
                <p className="text-[#E0E0E0]">{currentSection.description}</p>
              </div>
            </div>
            
            <div className="grid gap-4">
              {currentSection.articles.map(article => (
                <Card 
                  key={article.id}
                  className="cursor-pointer hover:bg-[#2a2847] transition-colors"
                  onClick={() => setActiveArticle(article.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-white mb-1">{article.title}</h3>
                        <div className="flex gap-2">
                          {article.tags.slice(0, 3).map(tag => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <ChevronRight className="h-5 w-5 text-gray-400" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        ) : (
          // Vue d'accueil
          <motion.div
            key="home"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {(searchTerm ? filteredSections : helpSections).map(section => (
              <Card 
                key={section.id}
                className="cursor-pointer hover:bg-[#2a2847] transition-colors"
                onClick={() => setActiveSection(section.id)}
              >
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {section.icon}
                    {section.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-[#E0E0E0] mb-4">{section.description}</p>
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary">
                      {section.articles.length} article{section.articles.length > 1 ? 's' : ''}
                    </Badge>
                    <ChevronRight className="h-5 w-5 text-gray-400" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default HelpCenter;
