
**Toujours vérifier ta mémoire et vérifier ensuite ce fichier. On ne sait jamais, je peux en cours de route te mettre de nouvelles instructions lorsque toi tu travailles.** 



# nous allons compléter les tâches. 

- La fonction ajouter nouvelle plante peut être améliorée. Pourquoi ? L'utilisateur pourrait apporter un descriptif par écrit de ce qu'il constate. Ça serait un plus. 

- Pour le moment, dans la partie calendrier, impossible de créer un nouvel événement. Voir capture d'écran de la console. 


- Ensuite, les pages Notifications, Journal, IA, Gemini. Les polices ont un problème, elles ne sont pas aux couleurs de l'application. Les titres et sous-titres. Aussi, vérifier des boutons qui ne sont pas aux couleurs justement de l'application. Voir deuxième capture. 


- Quand vous aurez fini tout ça, il ne manquera plus qu'une chose : créer dans le menu principal l'aide pour les utilisateurs parce qu'il faut bien leur expliquer comment tout ça fonctionne. ! 
Comme il y a pas mal d'explications, n'hésitez pas à faire plusieurs pages ou dans le menu principal si vous rajoutez l'aide, faites des sous-menus au pire des cas. Le bouton Aide dans le menu principal doit être placé après IA Gemini. 

Paramètres Gemini IA
Vous avez oublié les boutons en bas de page. Fonctionnalités IA activées, tous les boutons ont une couleur verte, ils ne sont pas aux couleurs de l'application. 

Commit GitHub =>> https://github.com/cisco-03/FloraSynth.git
Faites une vérification dans l'application. Aucun nom de "Violet Rikita" ne doit apparaître. L'application se nomme bien FloraSynth   **Le dossier Z-Archive aussi ne doit pas être commité, attention !**

Pour terminer, vérifiez que l'application est entièrement fonctionnelle, qu'il n'y a pas de problème de sécurité (_Framework d'Instructions), et ensuite, préparer l'application pour Netlify pour un déploiement. 
Attention, certains dossiers ne doivent pas être commités comme le framework d'instruction. Et bien sûr certains fichiers de sécurité comme les clés API qui doivent être en sécurité, etc. 


Parce que vous avez pensé dans le dossier Framework Instructions ? Est-ce que vous avez pensé dans le dossier Framework Instructions de créer un dossier au bon endroit pour vos notes de développement, d'amélioration et de débugage ? Je ne crois pas. Après, c'est vous qui décidez. Si vous ne le faites pas, après, ça va être beaucoup plus dur. Il faut une trace historique de tout ce que vous faites, parce que sinon, ça va être compliqué après. 




